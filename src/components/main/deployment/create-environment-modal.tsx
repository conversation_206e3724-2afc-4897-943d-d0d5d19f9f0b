'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useEnvironmentStore } from '@/store/environment/action';

const createEnvironmentSchema = z.object({
  name: z.string().min(1, 'Environment variable name is required'),
  value: z.string().min(1, 'Environment variable value is required'),
});

type CreateEnvironmentForm = z.infer<typeof createEnvironmentSchema>;

interface CreateEnvironmentModalProps {
  deploymentId: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function CreateEnvironmentModal({
  deploymentId,
  open,
  onOpenChange,
  onSuccess,
}: CreateEnvironmentModalProps) {
  const { createEnvironment, creating } = useEnvironmentStore();

  const form = useForm<CreateEnvironmentForm>({
    resolver: zodResolver(createEnvironmentSchema),
    defaultValues: {
      name: '',
      value: '',
    },
  });

  const onSubmit = async (data: CreateEnvironmentForm) => {
    try {
      const response = await createEnvironment({
        ...data,
        deployment_id: deploymentId,
      });

      if (response?.status) {
        toast.success('Environment variable created successfully');
        form.reset();
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error('Failed to create environment variable');
      }
    } catch (error) {
      console.error('Error creating environment variable:', error);
      toast.error('Failed to create environment variable');
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle>Add Environment Variable</DialogTitle>
          <DialogDescription>
            Add a new environment variable to this deployment.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Variable Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='e.g., DATABASE_URL'
                      {...field}
                      disabled={creating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='value'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Variable Value</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='e.g., postgresql://localhost:5432/myapp'
                      {...field}
                      disabled={creating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={handleClose}
                disabled={creating}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={creating}>
                {creating && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
                Add Variable
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
