'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { Loader2 } from 'lucide-react';
import { useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { toast } from 'sonner';
import { z } from 'zod';

import { <PERSON><PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { useEnvironmentStore } from '@/store/environment/action';
import { EnvironmentType } from '@/store/environment/type';

const editEnvironmentSchema = z.object({
  name: z.string().min(1, 'Environment variable name is required'),
  value: z.string().min(1, 'Environment variable value is required'),
});

type EditEnvironmentForm = z.infer<typeof editEnvironmentSchema>;

interface EditEnvironmentModalProps {
  environment: EnvironmentType;
  deploymentId: number;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSuccess?: () => void;
}

export function EditEnvironmentModal({
  environment,
  deploymentId,
  open,
  onOpenChange,
  onSuccess,
}: EditEnvironmentModalProps) {
  const { updateEnvironment, updating } = useEnvironmentStore();

  const form = useForm<EditEnvironmentForm>({
    resolver: zodResolver(editEnvironmentSchema),
    defaultValues: {
      name: environment.name,
      value: environment.value,
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        name: environment.name,
        value: environment.value,
      });
    }
  }, [open, environment, form]);

  const onSubmit = async (data: EditEnvironmentForm) => {
    try {
      const response = await updateEnvironment(environment.id, {
        ...data,
        deployment_id: deploymentId,
      });

      if (response?.status) {
        toast.success('Environment variable updated successfully');
        onOpenChange(false);
        onSuccess?.();
      } else {
        toast.error('Failed to update environment variable');
      }
    } catch (error) {
      console.error('Error updating environment variable:', error);
      toast.error('Failed to update environment variable');
    }
  };

  const handleClose = () => {
    form.reset();
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className='sm:max-w-[500px]'>
        <DialogHeader>
          <DialogTitle>Edit Environment Variable</DialogTitle>
          <DialogDescription>
            Update the environment variable for this deployment.
          </DialogDescription>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4'>
            <FormField
              control={form.control}
              name='name'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Variable Name</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='e.g., DATABASE_URL'
                      {...field}
                      disabled={updating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name='value'
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Variable Value</FormLabel>
                  <FormControl>
                    <Input
                      placeholder='e.g., postgresql://localhost:5432/myapp'
                      {...field}
                      disabled={updating}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <DialogFooter>
              <Button
                type='button'
                variant='outline'
                onClick={handleClose}
                disabled={updating}
              >
                Cancel
              </Button>
              <Button type='submit' disabled={updating}>
                {updating && <Loader2 className='mr-2 h-4 w-4 animate-spin' />}
                Update Variable
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
