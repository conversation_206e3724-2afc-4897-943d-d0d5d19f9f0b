'use client';

import { zodResolver } from '@hookform/resolvers/zod';
import { setCookie } from 'cookies-next';
import { useRouter } from 'next/navigation';
import { ComponentProps, useState } from 'react';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

import { loginAction } from '@/actions/user';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';

import { AuthCard } from './auth-card';

const signinSchema = z.object({
  email: z
    .string()
    .min(1, 'Username is required')
    .min(3, 'Username must be at least 3 characters'),
  password: z
    .string()
    .min(1, 'Password is required')
    .min(6, 'Password must be at least 6 characters'),
});

type SigninFormData = z.infer<typeof signinSchema>;

export function SigninForm({ className, ...props }: ComponentProps<'div'>) {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  // const searchParams = useSearchParams();
  // const redirectUrl = searchParams.get('redirect') || '/';

  const form = useForm<SigninFormData>({
    resolver: zodResolver(signinSchema),
    defaultValues: {
      email: '',
      password: '',
    },
  });

  const onSubmit = async (data: SigninFormData) => {
    setIsLoading(true);
    try {
      const result: any = await loginAction({
        email: data.email,
        password: data.password,
      });

      if (result.status) {
        await setCookie('access_token', result.data.token, {
          maxAge: 60 * 24 * 30,
        });
        // await router.push(redirectUrl);
        if (result.data.user.user_type.is_sale) {
          await router.push('/my-orders');
        } else {
          await router.push('/dashboard');
        }
        form.reset();
      } else {
        form.setError('root', {
          type: 'manual',
          message: 'Invalid username or password. Please try again.',
        });
      }
    } catch {
      form.setError('root', {
        type: 'manual',
        message: 'An error occurred. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthCard className={className} {...props}>
      <div className='flex flex-col items-center text-center'>
        <h1 className='text-2xl font-medium'>Welcome back</h1>
        <p className='text-muted-foreground text-balance'>
          Sign in to your Acme Inc account
        </p>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
          <FormField
            control={form.control}
            name='email'
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input
                    type='text'
                    placeholder='username'
                    {...field}
                    disabled={isLoading}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name='password'
            render={({ field }) => (
              <FormItem>
                <div className='flex items-center'>
                  <FormLabel>Password</FormLabel>
                  <a
                    href='#'
                    className='ml-auto text-sm underline-offset-2 hover:underline'
                  >
                    Forgot your password?
                  </a>
                </div>
                <FormControl>
                  <Input type='password' {...field} disabled={isLoading} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {form.formState.errors.root && (
            <div className='text-destructive text-sm text-center'>
              {form.formState.errors.root.message}
            </div>
          )}

          <Button type='submit' className='w-full' disabled={isLoading}>
            {isLoading ? 'Signing in...' : 'Sign In'}
          </Button>
        </form>
      </Form>

      {/*<SocialLoginButtons mode='signin' />*/}
      {/*<div className='text-center text-sm'>*/}
      {/*  Don&apos;t have an account?{' '}*/}
      {/*  <a href='/signup' className='underline underline-offset-4'>*/}
      {/*    Sign up*/}
      {/*  </a>*/}
      {/*</div>*/}
    </AuthCard>
  );
}
