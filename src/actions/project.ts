'use server';

import Http from '@/lib/http';

export async function getProjects(filters?: {
  type?: string;
  is_active?: boolean;
  cluster_id?: number;
  workspace_id?: number;
  name?: string;
}) {
  try {
    const searchParams = new URLSearchParams();

    if (filters?.type) {
      searchParams.append('type', filters.type);
    }
    if (filters?.is_active !== undefined) {
      searchParams.append('is_active', filters.is_active.toString());
    }
    if (filters?.cluster_id) {
      searchParams.append('cluster_id', filters.cluster_id.toString());
    }
    if (filters?.workspace_id) {
      searchParams.append('workspace_id', filters.workspace_id.toString());
    }
    if (filters?.name) {
      searchParams.append('name', filters.name);
    }

    const url = `/projects${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    const response = await Http.get(url);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function createProject(data: {
  name: string;
  slug: string;
  is_active: boolean;
  type: string;
  cluster_id: number;
  deployments: Array<{
    name: string;
    image: string;
    container_port: number;
    replicas: number;
    environments: Array<{
      name: string;
      value: string;
    }>;
    services: Array<{
      name: string;
      port: string;
      target_port: string;
      type: string;
    }>;
  }>;
}) {
  try {
    const response = await Http.post('/projects', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function createProjectFromTemplate(data: {
  name: string;
  template_id: number;
}) {
  try {
    const response = await Http.post('/projects/template', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function createProjectFromOrder(data: { order_id: number }) {
  try {
    const response = await Http.post('/projects/order', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getProject(id: number) {
  try {
    const response = await Http.get(`/projects/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
