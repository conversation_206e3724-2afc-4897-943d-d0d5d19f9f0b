'use server';

import Http from '@/lib/http';

export interface CreateIngressRequest {
  name: string;
  class: string;
  namespace_id: number;
}

export interface UpdateIngressRequest {
  name: string;
  class: string;
  status_id: number;
}

export async function createIngress(data: CreateIngressRequest) {
  try {
    const response = await Http.post('/ingresses', data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function updateIngress(id: number, data: UpdateIngressRequest) {
  try {
    const response = await Http.put(`/ingresses/${id}`, data);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function deleteIngress(id: number) {
  try {
    const response = await Http.delete(`/ingresses/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getIngress(id: number) {
  try {
    const response = await Http.get(`/ingresses/${id}`);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}

export async function getIngresses(filters?: {
  namespace_id?: number;
  name?: string;
  class?: string;
}) {
  try {
    const searchParams = new URLSearchParams();

    if (filters?.namespace_id) {
      searchParams.append('namespace_id', filters.namespace_id.toString());
    }
    if (filters?.name) {
      searchParams.append('name', filters.name);
    }
    if (filters?.class) {
      searchParams.append('class', filters.class);
    }

    const url = `/ingresses${searchParams.toString() ? `?${searchParams.toString()}` : ''}`;
    const response = await Http.get(url);
    return response.data;
  } catch (error) {
    console.log(error);
    throw error;
  }
}
