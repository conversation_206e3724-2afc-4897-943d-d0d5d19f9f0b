import { create } from 'zustand';

import {
  getIngresses,
  getIngress,
  createIngress as createIngress<PERSON><PERSON>,
  updateIngress as updateIngress<PERSON><PERSON>,
  deleteIngress as deleteIngressAPI,
} from '@/actions/ingress';
import {
  IngressActions,
  IngressStates,
  IngressType,
  CreateIngressRequest,
  UpdateIngressRequest,
  IngressFilters,
} from '@/store/ingress/type';

export const useIngressStore = create<IngressStates & IngressActions>(
  (set, get) => ({
    ingresses: [],
    selectedIngress: null,
    loading: false,
    creating: false,
    updating: false,
    deleting: false,

    setIngresses: (ingresses: IngressType[]) => set(() => ({ ingresses })),
    setSelectedIngress: (ingress: IngressType | null) =>
      set(() => ({ selectedIngress: ingress })),
    setLoading: (loading: boolean) => set(() => ({ loading })),
    setCreating: (creating: boolean) => set(() => ({ creating })),
    setUpdating: (updating: boolean) => set(() => ({ updating })),
    setDeleting: (deleting: boolean) => set(() => ({ deleting })),

    fetchIngresses: async (filters?: IngressFilters) => {
      set(() => ({ loading: true }));
      try {
        const response: any = await getIngresses(filters);
        set(() => ({ ingresses: response.data || [], loading: false }));
      } catch (error) {
        console.error('Error fetching ingresses:', error);
        set(() => ({ loading: false }));
        throw error;
      }
    },

    fetchIngress: async (id: number) => {
      set(() => ({ loading: true }));
      try {
        const response: any = await getIngress(id);
        set(() => ({ selectedIngress: response.data, loading: false }));
      } catch (error) {
        console.error('Error fetching ingress:', error);
        set(() => ({ loading: false }));
        throw error;
      }
    },

    createIngress: async (data: CreateIngressRequest) => {
      set(() => ({ creating: true }));
      try {
        const response = await createIngressAPI(data);

        // Refresh the ingresses list
        const { fetchIngresses } = get();
        await fetchIngresses();

        set(() => ({ creating: false }));
        return response;
      } catch (error) {
        console.error('Error creating ingress:', error);
        set(() => ({ creating: false }));
        throw error;
      }
    },

    updateIngress: async (id: number, data: UpdateIngressRequest) => {
      set(() => ({ updating: true }));
      try {
        const response: any = await updateIngressAPI(id, data);

        // Refresh the ingresses list
        const { fetchIngresses } = get();
        await fetchIngresses();

        set(() => ({ updating: false }));
        return response;
      } catch (error) {
        console.error('Error updating ingress:', error);
        set(() => ({ updating: false }));
        throw error;
      }
    },

    deleteIngress: async (id: number) => {
      set(() => ({ deleting: true }));
      try {
        const response = await deleteIngressAPI(id);

        // Refresh the ingresses list
        const { fetchIngresses } = get();
        await fetchIngresses();

        set(() => ({ deleting: false }));
        return response;
      } catch (error) {
        console.error('Error deleting ingress:', error);
        set(() => ({ deleting: false }));
        throw error;
      }
    },
  })
);
