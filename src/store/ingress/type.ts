export interface IngressType {
  id: number;
  created_at: string;
  updated_at: string;
  name: string;
  class: string;
  namespace_id: number;
  namespace?: any;
  status?: StatusType;
  ingress_specs?: IngressSpecType[];
}

export interface StatusType {
  id: number;
  name: string;
}

export interface IngressSpecType {
  id: number;
  host: string;
  path: string;
  port: number;
  service?: ServiceType;
}

export interface ServiceType {
  id: number;
  name: string;
  port: string;
  target_port: string;
  type: string;
  cluster_ip?: string;
  external_ip?: string;
}

export interface CreateIngressRequest {
  name: string;
  class: string;
  namespace_id: number;
}

export interface UpdateIngressRequest {
  name: string;
  class: string;
  status_id: number;
}

export interface IngressFilters {
  namespace_id?: number;
  name?: string;
  class?: string;
}

export interface IngressStates {
  ingresses: IngressType[];
  selectedIngress: IngressType | null;
  loading: boolean;
  creating: boolean;
  updating: boolean;
  deleting: boolean;
}

export interface IngressActions {
  setIngresses: (ingresses: IngressType[]) => void;
  setSelectedIngress: (ingress: IngressType | null) => void;
  setLoading: (loading: boolean) => void;
  setCreating: (creating: boolean) => void;
  setUpdating: (updating: boolean) => void;
  setDeleting: (deleting: boolean) => void;
  fetchIngresses: (filters?: IngressFilters) => Promise<void>;
  fetchIngress: (id: number) => Promise<void>;
  createIngress: (data: CreateIngressRequest) => Promise<any>;
  updateIngress: (id: number, data: UpdateIngressRequest) => Promise<any>;
  deleteIngress: (id: number) => Promise<any>;
}
